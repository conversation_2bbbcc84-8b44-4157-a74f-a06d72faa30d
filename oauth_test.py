#!/usr/bin/env python3
"""
OAuth Jira Upload Test
Sử dụng OAuth như tool MCP hiện tại
"""

import requests
import os
import json

def oauth_upload_test():
    """Test upload với OAuth authentication"""
    
    print("🚀 OAuth Jira Upload Test")
    print("=" * 50)
    
    # Thông tin OAuth (giống tool MCP)
    JIRA_URL = "https://urbox.atlassian.net"
    # Sử dụng token từ MCP config - có thể là OAuth token
    OAUTH_TOKEN = "ATATT3xFfGF0ecaHuny1fEOTWhegBAMFQza3mTBbmZ02ebJ9NF_Pej5oikaeHYv1I0rrG7LHtODbYT0bzM6Ow9KpSVT1eFWfR2349ToQRziVmxIzfna85LX2vrWky2LVdjxUu7-ZsIWNF0Syuj2Zg69o2YmmcAuYBbh5cv0XM0b3z7t9_KHGrVQ=B34D9C1B"
    ISSUE_KEY = "UBG-2792"
    
    print(f"🌐 Jira URL: {JIRA_URL}")
    print(f"🎯 Target Issue: {ISSUE_KEY}")
    
    # Tạo file test
    test_file = "oauth_test_upload.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("🧪 OAuth Test File Upload to Jira\n")
        f.write("=" * 40 + "\n")
        f.write(f"Created by: oauth_test.py\n")
        f.write(f"Target issue: {ISSUE_KEY}\n")
        f.write(f"Authentication: OAuth Bearer Token\n")
        f.write(f"Test purpose: Verify OAuth file upload\n")
        f.write("\n")
        f.write("This file tests OAuth-based upload to Jira\n")
        f.write("using the same authentication as MCP tool.\n")
    
    print(f"📝 Created test file: {test_file} ({os.path.getsize(test_file)} bytes)")
    
    # Test với Bearer token (OAuth)
    headers_oauth = {
        'Authorization': f'Bearer {OAUTH_TOKEN}',
        'Accept': 'application/json',
        'X-Atlassian-Token': 'no-check'
    }
    
    print("🔐 Testing OAuth Bearer authentication...")
    
    # Test 1: Connection với OAuth
    print("\n" + "-" * 30)
    print("🔐 Test 1: OAuth Connection...")
    try:
        response = requests.get(f"{JIRA_URL}/rest/api/3/myself", headers=headers_oauth, timeout=10)
        print(f"📊 OAuth Status: {response.status_code}")
        
        if response.status_code == 200:
            user = response.json()
            print(f"✅ OAuth connection successful!")
            print(f"👤 User: {user.get('displayName', 'Unknown')}")
            print(f"📧 Email: {user.get('emailAddress', 'Unknown')}")
            oauth_works = True
        else:
            print(f"❌ OAuth failed: {response.status_code}")
            print(f"📝 Error: {response.text}")
            oauth_works = False
            
    except Exception as e:
        print(f"❌ OAuth error: {e}")
        oauth_works = False
    
    # Nếu OAuth không work, thử Basic Auth
    if not oauth_works:
        print("\n🔄 Trying Basic Auth fallback...")
        import base64
        
        email = "<EMAIL>"
        auth_string = f"{email}:{OAUTH_TOKEN}"
        auth_bytes = auth_string.encode('utf-8')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers_basic = {
            'Authorization': f'Basic {auth_b64}',
            'Accept': 'application/json',
            'X-Atlassian-Token': 'no-check'
        }
        
        try:
            response = requests.get(f"{JIRA_URL}/rest/api/3/myself", headers=headers_basic, timeout=10)
            print(f"📊 Basic Auth Status: {response.status_code}")
            
            if response.status_code == 200:
                user = response.json()
                print(f"✅ Basic Auth successful!")
                print(f"👤 User: {user.get('displayName', 'Unknown')}")
                headers = headers_basic
                auth_type = "Basic Auth"
            else:
                print(f"❌ Basic Auth failed: {response.status_code}")
                print(f"📝 Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Basic Auth error: {e}")
            return False
    else:
        headers = headers_oauth
        auth_type = "OAuth Bearer"
    
    # Test 2: Get Issue
    print("\n" + "-" * 30)
    print(f"📋 Test 2: Getting issue {ISSUE_KEY}...")
    try:
        response = requests.get(
            f"{JIRA_URL}/rest/api/3/issue/{ISSUE_KEY}",
            headers=headers,
            params={'fields': 'summary,attachment,project'},
            timeout=10
        )
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            issue = response.json()
            print(f"✅ Issue found!")
            print(f"📝 Summary: {issue['fields']['summary']}")
            print(f"🏷️ Project: {issue['fields']['project']['key']}")
            
            attachments = issue['fields'].get('attachment', [])
            print(f"📎 Current attachments: {len(attachments)}")
            
        else:
            print(f"❌ Issue not found: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Issue error: {e}")
        return False
    
    # Test 3: Upload File
    print("\n" + "-" * 30)
    print(f"📎 Test 3: Uploading with {auth_type}...")
    try:
        url = f"{JIRA_URL}/rest/api/3/issue/{ISSUE_KEY}/attachments"
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/plain')}
            
            # Headers cho upload (bỏ Content-Type)
            upload_headers = headers.copy()
            upload_headers.pop('Content-Type', None)
            
            print(f"🔄 Uploading to: {url}")
            print(f"🔐 Auth method: {auth_type}")
            
            response = requests.post(url, headers=upload_headers, files=files, timeout=60)
            
            print(f"📊 Upload Status: {response.status_code}")
            print(f"📋 Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()[0]
                print("🎉 UPLOAD SUCCESSFUL!")
                print(f"🆔 Attachment ID: {result['id']}")
                print(f"📄 Filename: {result['filename']}")
                print(f"📏 Size: {result['size']} bytes")
                print(f"🔗 Content URL: {result['content']}")
                
                success = True
                
            elif response.status_code == 403:
                print("❌ UPLOAD FORBIDDEN (403)")
                print("🔍 Possible causes:")
                print("  - Insufficient permissions")
                print("  - Issue is in a restricted project")
                print("  - Attachment upload disabled")
                print(f"📝 Error: {response.text}")
                success = False
                
            elif response.status_code == 415:
                print("❌ UNSUPPORTED MEDIA TYPE (415)")
                print("🔍 This means the tool doesn't support multipart/form-data")
                print("📝 Error: {response.text}")
                success = False
                
            else:
                print(f"❌ UPLOAD FAILED: {response.status_code}")
                print(f"📝 Error: {response.text}")
                success = False
                
    except Exception as e:
        print(f"❌ Upload error: {e}")
        success = False
    
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🗑️ Cleaned up: {test_file}")
    
    return success

def main():
    print("🧪 Starting OAuth Jira Upload Test")
    print("=" * 60)
    
    success = oauth_upload_test()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 OAUTH TEST PASSED!")
        print("✅ File upload to Jira is working!")
        print("✅ Authentication and permissions are correct!")
    else:
        print("💥 OAUTH TEST FAILED!")
        print("❌ File upload not working with current setup")
        print("🔧 Possible solutions:")
        print("  1. Tool MCP doesn't support file upload")
        print("  2. Need different authentication method")
        print("  3. Create custom MCP tool with upload support")
    
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
