Test file for curl upload to Jira
Created: 2025-01-12
Purpose: Testing file upload with curl command

This is a test file to verify that curl can upload attachments to Jira issues.
The file will be uploaded to issue UBG-2792 using the new API token.

Content includes:
- Simple text format
- UTF-8 encoding
- Small file size for quick upload
- Test timestamp and metadata

If this upload succeeds, it confirms that:
✅ API token is valid
✅ Authentication is working
✅ File upload permissions are correct
✅ Jira API endpoint is accessible
